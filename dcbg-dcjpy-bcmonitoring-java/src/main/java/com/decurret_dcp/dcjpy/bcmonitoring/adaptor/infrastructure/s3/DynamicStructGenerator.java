package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import net.bytebuddy.ByteBuddy;
import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.description.modifier.Visibility;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.implementation.Implementation;
import net.bytebuddy.implementation.bytecode.ByteCodeAppender;
import net.bytebuddy.jar.asm.MethodVisitor;
import net.bytebuddy.jar.asm.Opcodes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.core.methods.response.AbiDefinition;

import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Dynamic class generator for creating DynamicStruct subclasses using ByteBuddy.
 * This eliminates the need for hardcoded struct classes by generating them dynamically
 * from ABI tuple definitions.
 */
public class DynamicStructGenerator {
    private static final Logger log = LoggerFactory.getLogger(DynamicStructGenerator.class);
    
    // Cache for generated classes to avoid regeneration
    private static final Map<String, Class<? extends DynamicStruct>> classCache = new ConcurrentHashMap<>();
    
    /**
     * Generate a DynamicStruct subclass for the given tuple components.
     * 
     * @param eventName The event name (used for class naming)
     * @param tupleComponents The tuple components from ABI definition
     * @return Generated class that extends DynamicStruct
     */
    public static Class<? extends DynamicStruct> generateDynamicStructClass(
            String eventName, 
            List<AbiDefinition.NamedType> tupleComponents) {
        
        // Create a unique class name based on event name and component signature
        String className = createClassName(eventName, tupleComponents);
        
        // Check cache first
        Class<? extends DynamicStruct> cachedClass = classCache.get(className);
        if (cachedClass != null) {
            log.debug("Using cached DynamicStruct class: {}", className);
            return cachedClass;
        }
        
        try {
            log.debug("Generating DynamicStruct class: {} with {} components",
                     className, tupleComponents.size());

            // Create parameter types array for constructor
            Class<?>[] parameterTypes = new Class<?>[tupleComponents.size()];
            for (int i = 0; i < tupleComponents.size(); i++) {
                parameterTypes[i] = Type.class; // All web3j types extend Type
            }

            // Generate the class using ByteBuddy with custom implementation
            DynamicType.Builder<DynamicStruct> builder = new ByteBuddy()
                .subclass(DynamicStruct.class)
                .name(className)
                .defineConstructor(Visibility.PUBLIC)
                .withParameters(parameterTypes)
                .intercept(new DynamicStructConstructorImplementation(tupleComponents.size()));

            // Create and load the class
            Class<? extends DynamicStruct> generatedClass = builder
                .make()
                .load(DynamicStructGenerator.class.getClassLoader())
                .getLoaded();

            // Cache the generated class
            classCache.put(className, generatedClass);

            log.info("Successfully generated DynamicStruct class: {}", className);
            return generatedClass;

        } catch (Exception e) {
            log.error("Failed to generate DynamicStruct class for event: {}", eventName, e);
            throw new RuntimeException("Failed to generate DynamicStruct class", e);
        }
    }
    
    /**
     * Create an instance of the generated DynamicStruct class.
     * 
     * @param structClass The generated class
     * @param values The values to pass to constructor
     * @return New instance of the struct
     */
    public static DynamicStruct createInstance(Class<? extends DynamicStruct> structClass, Type<?>... values) {
        try {
            // Find constructor with matching parameter count
            Constructor<?>[] constructors = structClass.getConstructors();
            Constructor<?> targetConstructor = null;
            
            for (Constructor<?> constructor : constructors) {
                if (constructor.getParameterCount() == values.length) {
                    targetConstructor = constructor;
                    break;
                }
            }
            
            if (targetConstructor == null) {
                throw new IllegalArgumentException(
                    String.format("No constructor found with %d parameters in class %s", 
                                values.length, structClass.getName()));
            }
            
            return (DynamicStruct) targetConstructor.newInstance((Object[]) values);
            
        } catch (Exception e) {
            log.error("Failed to create instance of class: {}", structClass.getName(), e);
            throw new RuntimeException("Failed to create DynamicStruct instance", e);
        }
    }
    
    /**
     * Create a unique class name based on event name and component types.
     */
    private static String createClassName(String eventName, List<AbiDefinition.NamedType> components) {
        StringBuilder sb = new StringBuilder();
        sb.append("Generated").append(eventName).append("Struct");
        
        // Add a hash of component types to ensure uniqueness
        int hash = 0;
        for (AbiDefinition.NamedType component : components) {
            hash = hash * 31 + component.getType().hashCode();
            if (component.getName() != null) {
                hash = hash * 31 + component.getName().hashCode();
            }
        }
        
        sb.append("_").append(Math.abs(hash));
        return sb.toString();
    }
    
    /**
     * Clear the class cache (useful for testing or memory management).
     */
    public static void clearCache() {
        classCache.clear();
        log.debug("Cleared DynamicStruct class cache");
    }
    
    /**
     * Get cache statistics for monitoring.
     */
    public static int getCacheSize() {
        return classCache.size();
    }
    
    /**
     * Custom implementation for DynamicStruct constructor.
     */
    private static class DynamicStructConstructorImplementation implements Implementation {
        private final int parameterCount;

        public DynamicStructConstructorImplementation(int parameterCount) {
            this.parameterCount = parameterCount;
        }

        @Override
        public InstrumentedType prepare(InstrumentedType instrumentedType) {
            return instrumentedType;
        }

        @Override
        public ByteCodeAppender appender(Target implementationTarget) {
            return new DynamicStructByteCodeAppender(parameterCount);
        }
    }

    /**
     * ByteCode appender for DynamicStruct constructor.
     */
    private static class DynamicStructByteCodeAppender implements ByteCodeAppender {
        private final int parameterCount;

        public DynamicStructByteCodeAppender(int parameterCount) {
            this.parameterCount = parameterCount;
        }

        @Override
        public Size apply(MethodVisitor methodVisitor, Context implementationContext, MethodDescription instrumentedMethod) {
            // Load 'this' onto stack
            methodVisitor.visitVarInsn(Opcodes.ALOAD, 0);

            // Create array for parameters
            methodVisitor.visitLdcInsn(parameterCount);
            methodVisitor.visitTypeInsn(Opcodes.ANEWARRAY, "org/web3j/abi/datatypes/Type");

            // Load each parameter into the array
            for (int i = 0; i < parameterCount; i++) {
                methodVisitor.visitInsn(Opcodes.DUP);
                methodVisitor.visitLdcInsn(i);
                methodVisitor.visitVarInsn(Opcodes.ALOAD, i + 1);
                methodVisitor.visitInsn(Opcodes.AASTORE);
            }

            // Convert array to List using Arrays.asList
            methodVisitor.visitMethodInsn(Opcodes.INVOKESTATIC,
                "java/util/Arrays", "asList", "([Ljava/lang/Object;)Ljava/util/List;", false);

            // Call super constructor
            methodVisitor.visitMethodInsn(Opcodes.INVOKESPECIAL,
                "org/web3j/abi/datatypes/DynamicStruct", "<init>", "(Ljava/util/List;)V", false);

            // Return
            methodVisitor.visitInsn(Opcodes.RETURN);

            return new Size(Math.max(2, parameterCount + 2), parameterCount + 1);
        }
    }
}
