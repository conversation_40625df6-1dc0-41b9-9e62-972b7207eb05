package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.core.methods.response.AbiDefinition;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple dynamic struct generator that creates DynamicStruct instances
 * without using ByteBuddy. This approach uses a factory pattern instead
 * of dynamic class generation.
 */
public class SimpleDynamicStructGenerator {
    private static final Logger log = LoggerFactory.getLogger(SimpleDynamicStructGenerator.class);
    
    // Cache for struct factories to avoid recreation
    private static final Map<String, DynamicStructFactory> factoryCache = new ConcurrentHashMap<>();
    
    /**
     * Get or create a factory for the given event and tuple components.
     * 
     * @param eventName The event name (used for factory naming)
     * @param tupleComponents The tuple components from ABI definition
     * @return Factory that can create DynamicStruct instances
     */
    public static DynamicStructFactory getStructFactory(
            String eventName, 
            List<AbiDefinition.NamedType> tupleComponents) {
        
        // Create a unique factory key based on event name and component signature
        String factoryKey = createFactoryKey(eventName, tupleComponents);
        
        // Check cache first
        DynamicStructFactory cachedFactory = factoryCache.get(factoryKey);
        if (cachedFactory != null) {
            log.debug("Using cached DynamicStruct factory: {}", factoryKey);
            return cachedFactory;
        }
        
        // Create new factory
        DynamicStructFactory factory = new DynamicStructFactory(eventName, tupleComponents);
        factoryCache.put(factoryKey, factory);
        
        log.info("Created new DynamicStruct factory: {}", factoryKey);
        return factory;
    }
    
    /**
     * Create a unique factory key based on event name and component types.
     */
    private static String createFactoryKey(String eventName, List<AbiDefinition.NamedType> components) {
        StringBuilder sb = new StringBuilder();
        sb.append(eventName);
        
        // Add a hash of component types to ensure uniqueness
        int hash = 0;
        for (AbiDefinition.NamedType component : components) {
            hash = hash * 31 + component.getType().hashCode();
            if (component.getName() != null) {
                hash = hash * 31 + component.getName().hashCode();
            }
        }
        
        sb.append("_").append(Math.abs(hash));
        return sb.toString();
    }
    
    /**
     * Clear the factory cache (useful for testing or memory management).
     */
    public static void clearCache() {
        factoryCache.clear();
        log.debug("Cleared DynamicStruct factory cache");
    }
    
    /**
     * Get cache statistics for monitoring.
     */
    public static int getCacheSize() {
        return factoryCache.size();
    }
    
    /**
     * Factory class for creating DynamicStruct instances.
     */
    public static class DynamicStructFactory {
        private final String eventName;
        private final List<AbiDefinition.NamedType> tupleComponents;
        private final int componentCount;
        
        public DynamicStructFactory(String eventName, List<AbiDefinition.NamedType> tupleComponents) {
            this.eventName = eventName;
            this.tupleComponents = tupleComponents;
            this.componentCount = tupleComponents.size();
        }
        
        /**
         * Create a DynamicStruct instance with the given values.
         * 
         * @param values The values to include in the struct
         * @return New DynamicStruct instance
         */
        public DynamicStruct createInstance(Type<?>... values) {
            if (values.length != componentCount) {
                throw new IllegalArgumentException(
                    String.format("Expected %d values but got %d for event %s", 
                                componentCount, values.length, eventName));
            }
            
            return new DynamicStruct(Arrays.asList(values));
        }
        
        /**
         * Create a DynamicStruct instance with the given values as a list.
         * 
         * @param values The values to include in the struct
         * @return New DynamicStruct instance
         */
        public DynamicStruct createInstance(List<Type<?>> values) {
            if (values.size() != componentCount) {
                throw new IllegalArgumentException(
                    String.format("Expected %d values but got %d for event %s", 
                                componentCount, values.size(), eventName));
            }
            
            return new DynamicStruct(values);
        }
        
        /**
         * Get the event name this factory is for.
         */
        public String getEventName() {
            return eventName;
        }
        
        /**
         * Get the tuple components this factory handles.
         */
        public List<AbiDefinition.NamedType> getTupleComponents() {
            return tupleComponents;
        }
        
        /**
         * Get the expected number of components.
         */
        public int getComponentCount() {
            return componentCount;
        }
        
        /**
         * Get component information for debugging.
         */
        public String getComponentInfo() {
            StringBuilder sb = new StringBuilder();
            sb.append("Components for ").append(eventName).append(": [");
            for (int i = 0; i < tupleComponents.size(); i++) {
                if (i > 0) sb.append(", ");
                AbiDefinition.NamedType component = tupleComponents.get(i);
                sb.append(component.getName()).append(":").append(component.getType());
            }
            sb.append("]");
            return sb.toString();
        }
    }
}
