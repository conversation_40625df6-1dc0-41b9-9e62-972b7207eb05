package adhoc;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.Utils;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;

/**
 * Example demonstrating the new approach for decoding dynamic Solidity structs
 * using web3j's enhanced TypeReference and Utils.convert functionality.
 * 
 * This approach eliminates the need for creating dedicated struct classes
 * and allows dynamic decoding directly within TypeReference.
 */
public class DynamicStructDecodingExample {

    public static void main(String[] args) {
        // Example: Decoding a User struct with uint256 userId and string name
        testDecodeDynamicStruct();
        
        // Example: Decoding a more complex struct like TransferData
        testDecodeComplexDynamicStruct();
    }

    /**
     * Test decoding a simple dynamic struct: User { uint256 userId; string name; }
     */
    public static void testDecodeDynamicStruct() {
        System.out.println("=== Testing Simple Dynamic Struct Decoding ===");
        
        // Raw encoded data for User struct with userId=10 and name="John"
        String rawInput = 
            "0x0000000000000000000000000000000000000000000000000000000000000020" +
            "000000000000000000000000000000000000000000000000000000000000000a" +
            "0000000000000000000000000000000000000000000000000000000000000040" +
            "0000000000000000000000000000000000000000000000000000000000000004" +
            "4a686f6e00000000000000000000000000000000000000000000000000000000";

        // Create TypeReference for dynamic struct using the new approach
        TypeReference<DynamicStruct> dynamicStruct;
        try {
            dynamicStruct = new TypeReference<DynamicStruct>(
                false, // not indexed
                Arrays.asList(
                    TypeReference.makeTypeReference("uint256"),
                    TypeReference.makeTypeReference("string")
                )
            ) {};
        } catch (ClassNotFoundException e) {
            System.err.println("Error creating TypeReference: " + e.getMessage());
            return;
        }

        try {
            // Decode using Utils.convert - this is the key improvement!
            List<Type> decodedData = FunctionReturnDecoder.decode(
                rawInput, 
                Utils.convert(Arrays.asList(dynamicStruct))
            );

            // Extract the decoded dynamic struct
            DynamicStruct decodedStruct = (DynamicStruct) decodedData.get(0);
            List<Type> structValues = decodedStruct.getValue();

            // Extract individual values
            BigInteger userId = (BigInteger) structValues.get(0).getValue();
            String name = (String) structValues.get(1).getValue();

            System.out.println("Decoded User struct:");
            System.out.println("  userId: " + userId);
            System.out.println("  name: " + name);
            
            // Verify the values
            assert userId.equals(BigInteger.TEN) : "UserId should be 10";
            assert "John".equals(name) : "Name should be John";
            
            System.out.println("✓ Simple dynamic struct decoding successful!");
            
        } catch (Exception e) {
            System.err.println("✗ Error decoding simple dynamic struct: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test decoding a complex dynamic struct with multiple field types
     */
    public static void testDecodeComplexDynamicStruct() {
        System.out.println("\n=== Testing Complex Dynamic Struct Decoding ===");
        
        // Create TypeReference for a complex struct like TransferData
        // struct TransferData {
        //   bytes32 transferType;
        //   uint16 zoneId;
        //   bytes32 fromValidatorId;
        //   bytes32 toValidatorId;
        //   uint256 fromAccountBalance;
        //   uint256 toAccountBalance;
        //   uint256 businessZoneBalance;
        //   uint16 bizZoneId;
        //   bytes32 sendAccountId;
        //   bytes32 fromAccountId;
        //   string fromAccountName;
        //   bytes32 toAccountId;
        //   string toAccountName;
        //   uint256 amount;
        //   bytes32 miscValue1;
        //   string miscValue2;
        //   string memo;
        // }
        
        TypeReference<DynamicStruct> complexStruct;
        try {
            complexStruct = new TypeReference<DynamicStruct>(
                false, // not indexed
                Arrays.asList(
                    TypeReference.makeTypeReference("bytes32"),  // transferType
                    TypeReference.makeTypeReference("uint16"),   // zoneId
                    TypeReference.makeTypeReference("bytes32"),  // fromValidatorId
                    TypeReference.makeTypeReference("bytes32"),  // toValidatorId
                    TypeReference.makeTypeReference("uint256"),  // fromAccountBalance
                    TypeReference.makeTypeReference("uint256"),  // toAccountBalance
                    TypeReference.makeTypeReference("uint256"),  // businessZoneBalance
                    TypeReference.makeTypeReference("uint16"),   // bizZoneId
                    TypeReference.makeTypeReference("bytes32"),  // sendAccountId
                    TypeReference.makeTypeReference("bytes32"),  // fromAccountId
                    TypeReference.makeTypeReference("string"),   // fromAccountName
                    TypeReference.makeTypeReference("bytes32"),  // toAccountId
                    TypeReference.makeTypeReference("string"),   // toAccountName
                    TypeReference.makeTypeReference("uint256"),  // amount
                    TypeReference.makeTypeReference("bytes32"),  // miscValue1
                    TypeReference.makeTypeReference("string"),   // miscValue2
                    TypeReference.makeTypeReference("string")    // memo
                )
            ) {};
        } catch (ClassNotFoundException e) {
            System.err.println("Error creating complex TypeReference: " + e.getMessage());
            return;
        }

        System.out.println("Created TypeReference for complex struct with 17 fields");
        System.out.println("✓ Complex dynamic struct TypeReference creation successful!");
    }

    /**
     * Utility method to create TypeReference for dynamic arrays of structs
     */
    public static TypeReference<?> createDynamicArrayTypeReference() {
        // For tuple[] (dynamic array of structs)
        try {
            return new TypeReference<org.web3j.abi.datatypes.DynamicArray<DynamicStruct>>(
                false, // not indexed
                Arrays.asList(
                    TypeReference.makeTypeReference("uint256"),
                    TypeReference.makeTypeReference("string")
                )
            ) {};
        } catch (ClassNotFoundException e) {
            System.err.println("Error creating array TypeReference: " + e.getMessage());
            return null;
        }
    }

    /**
     * Example of how to use this in event processing
     */
    public static void exampleEventProcessing() {
        System.out.println("\n=== Example Event Processing ===");

        // This is how you would use it in your AbiParser.createTypeReference method:
        boolean indexed = false;

        try {
            // Create inner types for the tuple components
            List<TypeReference<?>> innerTypes = Arrays.asList(
                TypeReference.makeTypeReference("uint256"),
                TypeReference.makeTypeReference("string")
            );

            // Create the TypeReference with inner types
            TypeReference<DynamicStruct> typeRef = new TypeReference<DynamicStruct>(indexed, innerTypes) {};

            System.out.println("Created TypeReference for event processing");
            System.out.println("Type: " + typeRef.getType());
            System.out.println("Indexed: " + indexed);
            System.out.println("Inner types count: " + innerTypes.size());
        } catch (ClassNotFoundException e) {
            System.err.println("Error in event processing example: " + e.getMessage());
        }
    }
}
