package adhoc;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.DynamicStructGenerator;
import org.junit.jupiter.api.Test;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.protocol.core.methods.response.AbiDefinition;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for DynamicStructGenerator to verify ByteBuddy-based dynamic class generation.
 */
public class DynamicStructGeneratorTest {

    @Test
    public void testGenerateDynamicStructClass() {
        System.out.println("=== Testing Dynamic Struct Class Generation ===");
        
        // Create mock tuple components for a simple User struct: { uint256 userId; string name; }
        List<AbiDefinition.NamedType> tupleComponents = Arrays.asList(
            createNamedType("userId", "uint256", false),
            createNamedType("name", "string", false)
        );
        
        // Generate the dynamic struct class
        Class<? extends DynamicStruct> generatedClass = DynamicStructGenerator
            .generateDynamicStructClass("User", tupleComponents);
        
        // Verify the class was generated
        assertNotNull(generatedClass);
        assertTrue(DynamicStruct.class.isAssignableFrom(generatedClass));
        assertTrue(generatedClass.getName().contains("GeneratedUserStruct"));
        
        System.out.println("✓ Generated class: " + generatedClass.getName());
        System.out.println("✓ Extends DynamicStruct: " + DynamicStruct.class.isAssignableFrom(generatedClass));
    }
    
    @Test
    public void testCreateInstanceOfGeneratedClass() {
        System.out.println("=== Testing Instance Creation ===");
        
        // Create mock tuple components
        List<AbiDefinition.NamedType> tupleComponents = Arrays.asList(
            createNamedType("userId", "uint256", false),
            createNamedType("name", "string", false)
        );
        
        // Generate the dynamic struct class
        Class<? extends DynamicStruct> generatedClass = DynamicStructGenerator
            .generateDynamicStructClass("User", tupleComponents);
        
        // Create instance with test data
        Uint256 userId = new Uint256(BigInteger.valueOf(42));
        Utf8String name = new Utf8String("Alice");
        
        DynamicStruct instance = DynamicStructGenerator.createInstance(generatedClass, userId, name);
        
        // Verify the instance
        assertNotNull(instance);
        assertEquals(generatedClass, instance.getClass());
        
        // Verify the values
        List<Type> values = instance.getValue();
        assertEquals(2, values.size());
        assertEquals(BigInteger.valueOf(42), values.get(0).getValue());
        assertEquals("Alice", values.get(1).getValue());
        
        System.out.println("✓ Created instance: " + instance.getClass().getName());
        System.out.println("✓ UserId: " + values.get(0).getValue());
        System.out.println("✓ Name: " + values.get(1).getValue());
    }
    
    @Test
    public void testClassCaching() {
        System.out.println("=== Testing Class Caching ===");
        
        // Create same tuple components twice
        List<AbiDefinition.NamedType> tupleComponents1 = Arrays.asList(
            createNamedType("userId", "uint256", false),
            createNamedType("name", "string", false)
        );
        
        List<AbiDefinition.NamedType> tupleComponents2 = Arrays.asList(
            createNamedType("userId", "uint256", false),
            createNamedType("name", "string", false)
        );
        
        // Generate classes
        Class<? extends DynamicStruct> class1 = DynamicStructGenerator
            .generateDynamicStructClass("User", tupleComponents1);
        Class<? extends DynamicStruct> class2 = DynamicStructGenerator
            .generateDynamicStructClass("User", tupleComponents2);
        
        // Verify they are the same cached instance
        assertSame(class1, class2);
        
        System.out.println("✓ Class caching works: " + (class1 == class2));
        System.out.println("✓ Cache size: " + DynamicStructGenerator.getCacheSize());
    }
    
    @Test
    public void testDifferentEventNames() {
        System.out.println("=== Testing Different Event Names ===");
        
        // Same components but different event names should generate different classes
        List<AbiDefinition.NamedType> tupleComponents = Arrays.asList(
            createNamedType("value", "uint256", false)
        );
        
        Class<? extends DynamicStruct> userClass = DynamicStructGenerator
            .generateDynamicStructClass("User", tupleComponents);
        Class<? extends DynamicStruct> transferClass = DynamicStructGenerator
            .generateDynamicStructClass("Transfer", tupleComponents);
        
        // Verify they are different classes
        assertNotSame(userClass, transferClass);
        assertTrue(userClass.getName().contains("User"));
        assertTrue(transferClass.getName().contains("Transfer"));
        
        System.out.println("✓ User class: " + userClass.getName());
        System.out.println("✓ Transfer class: " + transferClass.getName());
    }
    
    @Test
    public void testComplexTupleStructure() {
        System.out.println("=== Testing Complex Tuple Structure ===");
        
        // Create a more complex tuple structure similar to Transfer event
        List<AbiDefinition.NamedType> tupleComponents = Arrays.asList(
            createNamedType("transferType", "bytes32", false),
            createNamedType("zoneId", "uint16", false),
            createNamedType("fromValidatorId", "bytes32", false),
            createNamedType("amount", "uint256", false),
            createNamedType("memo", "string", false)
        );
        
        // Generate the dynamic struct class
        Class<? extends DynamicStruct> generatedClass = DynamicStructGenerator
            .generateDynamicStructClass("Transfer", tupleComponents);
        
        // Verify the class was generated
        assertNotNull(generatedClass);
        assertTrue(DynamicStruct.class.isAssignableFrom(generatedClass));
        
        System.out.println("✓ Generated complex struct class: " + generatedClass.getName());
        System.out.println("✓ Component count: " + tupleComponents.size());
    }
    
    /**
     * Helper method to create AbiDefinition.NamedType for testing.
     */
    private AbiDefinition.NamedType createNamedType(String name, String type, boolean indexed) {
        AbiDefinition.NamedType namedType = new AbiDefinition.NamedType();
        namedType.setName(name);
        namedType.setType(type);
        namedType.setIndexed(indexed);
        return namedType;
    }
}
